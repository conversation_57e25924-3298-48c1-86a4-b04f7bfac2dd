"use client";

import { useState, useRef } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Upload,
  FileText,
  Image,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import {
  extractTemplateName,
  hasImageReferences,
  extractImageSources,
  isValidHtmlFile,
} from "@/lib/template-utils";

interface UploadState {
  stage: "initial" | "images" | "uploading" | "success" | "error";
  htmlFile: File | null;
  templateName: string;
  description: string;
  layoutSize: string;
  imageFiles: File[];
  requiredImages: string[];
  error: string;
  success: string;
}

export default function AddTemplatePage() {
  const [state, setState] = useState<UploadState>({
    stage: "initial",
    htmlFile: null,
    templateName: "",
    description: "",
    layoutSize: "A4",
    imageFiles: [],
    requiredImages: [],
    error: "",
    success: "",
  });

  const htmlFileRef = useRef<HTMLInputElement>(null);
  const imageFilesRef = useRef<HTMLInputElement>(null);

  const handleHtmlFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!isValidHtmlFile(file.name)) {
      setState((prev) => ({
        ...prev,
        error: "Please select a valid HTML file (.htm or .html)",
        success: "",
      }));
      return;
    }

    try {
      // Read file content to check for images
      const content = await file.text();
      const hasImages = hasImageReferences(content);
      const imageSources = hasImages ? extractImageSources(content) : [];

      // Auto-populate template name from filename
      const autoTemplateName = extractTemplateName(file.name);

      setState((prev) => ({
        ...prev,
        htmlFile: file,
        templateName: prev.templateName || autoTemplateName,
        requiredImages: imageSources,
        stage: hasImages ? "images" : "initial",
        error: "",
        success: hasImages
          ? `HTML file loaded. This template requires ${imageSources.length} image(s). Please upload the image files.`
          : "HTML file loaded successfully.",
      }));
    } catch {
      setState((prev) => ({
        ...prev,
        error: "Failed to read HTML file content",
        success: "",
      }));
    }
  };

  const handleImageFilesChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = Array.from(event.target.files || []);
    setState((prev) => ({
      ...prev,
      imageFiles: files,
      success:
        files.length > 0 ? `${files.length} image file(s) selected.` : "",
      error: "",
    }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!state.htmlFile) {
      setState((prev) => ({
        ...prev,
        error: "Please select an HTML file",
        success: "",
      }));
      return;
    }

    if (state.requiredImages.length > 0 && state.imageFiles.length === 0) {
      setState((prev) => ({
        ...prev,
        error:
          "This template requires image files. Please upload the associated images.",
        success: "",
      }));
      return;
    }

    setState((prev) => ({
      ...prev,
      stage: "uploading",
      error: "",
      success: "",
    }));

    try {
      // Get user ID from localStorage (assuming admin is authenticated)
      const adminAuth = localStorage.getItem("ldis-admin-authenticated");
      if (!adminAuth) {
        throw new Error("Admin authentication required");
      }

      // Get current user information
      const userResponse = await fetch("/api/auth/me");
      if (!userResponse.ok) {
        throw new Error("Failed to get user information");
      }
      const userData = await userResponse.json();

      const formData = new FormData();
      formData.append("templateName", state.templateName);
      formData.append("description", state.description);
      formData.append("layoutSize", state.layoutSize);
      formData.append("userId", userData.id.toString());
      formData.append("htmlFile", state.htmlFile);

      // Add image files
      state.imageFiles.forEach((file) => {
        formData.append("imageFiles", file);
      });

      const response = await fetch("/api/templates/upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        setState((prev) => ({
          ...prev,
          stage: "success",
          success: `Template "${result.templateName}" uploaded successfully! Found ${result.placeholders.length} placeholders.`,
          error: "",
        }));

        // Reset form
        setTimeout(() => {
          setState({
            stage: "initial",
            htmlFile: null,
            templateName: "",
            description: "",
            layoutSize: "A4",
            imageFiles: [],
            requiredImages: [],
            error: "",
            success: "",
          });
          if (htmlFileRef.current) htmlFileRef.current.value = "";
          if (imageFilesRef.current) imageFilesRef.current.value = "";
        }, 3000);
      } else {
        setState((prev) => ({
          ...prev,
          stage: state.requiredImages.length > 0 ? "images" : "initial",
          error: result.error || "Upload failed",
          success: "",
        }));
      }
    } catch {
      setState((prev) => ({
        ...prev,
        stage: state.requiredImages.length > 0 ? "images" : "initial",
        error: "Network error. Please try again.",
        success: "",
      }));
    }
  };

  const resetForm = () => {
    setState({
      stage: "initial",
      htmlFile: null,
      templateName: "",
      description: "",
      layoutSize: "A4",
      imageFiles: [],
      requiredImages: [],
      error: "",
      success: "",
    });
    if (htmlFileRef.current) htmlFileRef.current.value = "";
    if (imageFilesRef.current) imageFilesRef.current.value = "";
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Add Template</h1>
        <p className="text-muted-foreground">
          Upload HTML templates for document generation
        </p>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Template Upload
          </CardTitle>
          <CardDescription>
            Upload an HTML template file. If your template contains images,
            you&apos;ll be prompted to upload them as well.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* HTML File Upload */}
            <div className="space-y-2">
              <Label htmlFor="htmlFile" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                HTML Template File *
              </Label>
              <Input
                id="htmlFile"
                ref={htmlFileRef}
                type="file"
                accept=".htm,.html"
                onChange={handleHtmlFileChange}
                disabled={state.stage === "uploading"}
                className="cursor-pointer"
              />
              {state.htmlFile && (
                <p className="text-sm text-muted-foreground">
                  Selected: {state.htmlFile.name} (
                  {(state.htmlFile.size / 1024).toFixed(1)} KB)
                </p>
              )}
            </div>

            {/* Image Files Upload - Only shown if HTML contains image references */}
            {(state.stage === "images" || state.stage === "uploading") &&
              state.requiredImages.length > 0 && (
                <div className="space-y-2">
                  <Label
                    htmlFor="imageFiles"
                    className="flex items-center gap-2"
                  >
                    <Image className="h-4 w-4" />
                    Image Files *
                  </Label>
                  <Input
                    id="imageFiles"
                    ref={imageFilesRef}
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageFilesChange}
                    disabled={state.stage === "uploading"}
                    className="cursor-pointer"
                  />
                  <div className="text-sm text-muted-foreground">
                    <p>Required images found in template:</p>
                    <ul className="list-disc list-inside ml-4">
                      {state.requiredImages.map((src, index) => (
                        <li key={index}>{src}</li>
                      ))}
                    </ul>
                  </div>
                  {state.imageFiles.length > 0 && (
                    <div className="text-sm text-muted-foreground">
                      <p>Selected files:</p>
                      <ul className="list-disc list-inside ml-4">
                        {state.imageFiles.map((file, index) => (
                          <li key={index}>
                            {file.name} ({(file.size / 1024).toFixed(1)} KB)
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

            {/* Template Name */}
            <div className="space-y-2">
              <Label htmlFor="templateName">Template Name *</Label>
              <Input
                id="templateName"
                value={state.templateName}
                onChange={(e) =>
                  setState((prev) => ({
                    ...prev,
                    templateName: e.target.value,
                  }))
                }
                placeholder="Enter template name"
                disabled={state.stage === "uploading"}
                required
              />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={state.description}
                onChange={(e) =>
                  setState((prev) => ({ ...prev, description: e.target.value }))
                }
                placeholder="Describe the purpose of this template"
                disabled={state.stage === "uploading"}
                rows={3}
              />
            </div>

            {/* Layout Size */}
            <div className="space-y-2">
              <Label htmlFor="layoutSize">Layout Size</Label>
              <Select
                value={state.layoutSize}
                onValueChange={(value) =>
                  setState((prev) => ({ ...prev, layoutSize: value }))
                }
                disabled={state.stage === "uploading"}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select layout size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A4">A4</SelectItem>
                  <SelectItem value="Letter">Letter</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Error/Success Messages */}
            {state.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{state.error}</AlertDescription>
              </Alert>
            )}

            {state.success && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>{state.success}</AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={
                  !state.htmlFile ||
                  state.stage === "uploading" ||
                  (state.requiredImages.length > 0 &&
                    state.imageFiles.length === 0)
                }
                className="flex items-center gap-2"
              >
                {state.stage === "uploading" ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4" />
                    Upload Template
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={resetForm}
                disabled={state.stage === "uploading"}
              >
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
